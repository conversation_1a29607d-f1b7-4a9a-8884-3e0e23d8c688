<?php
// 测试图片路径生成
define('SITE_PATH', __DIR__);
define('APP_PATH', SITE_PATH.'/Application/');

// 模拟HTTPS环境
$_SERVER['HTTPS'] = 'on';
$_SERVER['HTTP_HOST'] = 'station.zhongcaiguoke.cn';

// 引入配置
require APP_PATH.'/config.php';
require SITE_PATH.'/Core/ThinkPHP.php';

// 测试图片路径生成
echo "测试图片路径生成:\n";
echo "HTTPS环境: " . (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off' ? 'YES' : 'NO') . "\n";
echo "__HOST__: " . (defined('__HOST__') ? __HOST__ : 'NOT DEFINED') . "\n";
echo "__ROOT__: " . (defined('__ROOT__') ? __ROOT__ : 'NOT DEFINED') . "\n";
echo "UPLOADPATH: " . C('UPLOADPATH') . "\n";

// 测试imgPath函数
if (function_exists('imgPath')) {
    echo "imgPath('test.jpg'): " . imgPath('test.jpg') . "\n";
    echo "imgPath(''): " . imgPath('') . "\n";
} else {
    echo "imgPath function not found\n";
}
?>
