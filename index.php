<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006-2014 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------

// 应用入口文件

// 检测PHP环境
if(version_compare(PHP_VERSION,'5.3.0','<'))  die('require PHP > 5.3.0 !');

// 定义应用目录
define('SITE_PATH', dirname(__FILE__));

define('STATIC_PATH', SITE_PATH.'/static/');

if (PHP_SAPI != 'cli' && isset($_SERVER['HTTP_HOST'])) {
    $host = $_SERVER['HTTP_HOST'];
    // 自动检测协议
    $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off')
                || $_SERVER['SERVER_PORT'] == 443
                || (isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] === 'https')
                ? 'https://' : 'http://';
    define('__HOST__', $protocol.$_SERVER['HTTP_HOST']);
}

// 定义应用目录
!defined('APP_PATH') && define('APP_PATH', SITE_PATH.'/Application/');
if (file_exists(APP_PATH.'/config.php')) {
    require APP_PATH.'/config.php';
}

// 引入ThinkPHP入口文件
require SITE_PATH.'/Core/ThinkPHP.php';
// 亲^_^ 后面不需要任何代码了 就是如此简单
